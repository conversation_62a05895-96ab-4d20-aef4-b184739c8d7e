name: PR-check

on:
  pull_request:

concurrency:
  group: lint-${{ github.ref_name }}
  cancel-in-progress: true

jobs:
  lint:
    runs-on: ubuntu-latest
    #runs-on: windows-latest
    steps:
      - name: Checkout 🛎️
        uses: actions/checkout@v3

      - name: Install Node.js 🔧
        uses: actions/setup-node@v3
        with:
          node-version: 20.11.1

      - name: Install Deps 🔧
        run: |
          npm install -g pnpm@10.10.0
          pnpm install --frozen-lockfile

      - name: Lint PR (prettier)
        run: |
          pnpm run prettier:ci

      - name: Lint PR (eslint)
        run: |
          pnpm run lint:ci