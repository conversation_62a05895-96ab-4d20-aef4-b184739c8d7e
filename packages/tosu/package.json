{"name": "tosu", "version": "4.13.0", "main": "dist/index.js", "bin": "dist/index.js", "scripts": {"genver": "npx genversion _version.js", "ts:run": "cross-env NODE_ENV=development ts-node --transpile-only -r tsconfig-paths/register --project tsconfig.json", "ts:compile": "ncc build src/index.ts -o dist -m -d", "run:dev": "pnpm run genver && pnpm run ts:run src/index.ts", "compile:prepare-htmls": "cp -rf node_modules/@tosu/server/assets ./dist", "compile:win": "pnpm run genver && pnpm run ts:compile && pnpm run compile:prepare-htmls && pkg --output dist/tosu.exe --debug --config pkg.win.json --compress brotli dist/index.js && pnpm run ts:run src/postBuild.ts", "compile:linux": "pnpm run genver && pnpm run ts:compile && pnpm run compile:prepare-htmls && pkg --output dist/tosu --debug --config pkg.linux.json --compress brotli dist/index.js"}, "dependencies": {"@kotrikd/rosu-pp": "3.1.0", "@tosu/common": "workspace:*", "@tosu/ingame-overlay-updater": "workspace:*", "@tosu/server": "workspace:*", "@tosu/updater": "workspace:*", "osu-catch-stable": "^4.0.0", "osu-classes": "^3.1.0", "osu-mania-stable": "^5.0.0", "osu-parsers": "^4.1.7", "osu-standard-stable": "^5.0.0", "osu-taiko-stable": "^5.0.0", "resedit": "^2.0.3", "semver": "^7.7.1", "tsprocess": "workspace:*"}, "devDependencies": {"@vercel/ncc": "^0.38.3", "@yao-pkg/pkg": "^6.3.2", "cross-env": "^7.0.3", "genversion": "^3.2.0", "ts-node": "^10.9.2"}}