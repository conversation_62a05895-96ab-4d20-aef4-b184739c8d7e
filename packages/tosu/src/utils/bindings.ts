export enum Bindings {
    None,
    _Standard,
    OsuLeft,
    OsuRight,
    OsuSmoke,
    _TaikoMod,
    TaikoInnerLeft,
    TaikoInnerRight,
    TaikoOuterLeft,
    TaikoOuterRight,
    _CatchTheBeatMod,
    FruitsLeft,
    FruitsRight,
    FruitsDash,
    _ManiaMod,
    IncreaseSpeed,
    DecreaseSpeed,
    _InGame,
    Pause,
    Skip,
    ToggleScoreboard,
    IncreaseAudioOffset,
    DecreaseAudioOffset,
    QuickRetry,
    _General,
    ToggleFrameLimiter,
    ToggleChat,
    ToggleExtendedChat,
    Screenshot,
    VolumeIncrease,
    VolumeDecrease,
    DisableMouseButtons,
    BossKey,
    _Editor,
    SelectTool,
    NormalTool,
    SliderTool,
    SpinnerTool,
    NewComboToggle,
    WhistleToggle,
    FinishToggle,
    ClapToggle,
    GridSnapToggle,
    DistSnapToggle,
    NoteLockToggle,
    NudgeLeft,
    NudgeRight,
    HelpToggle,
    JumpToBegin,
    PlayFromBegin,
    AudioPause,
    JumpToEnd,
    GridChange,
    TimingSection,
    InheritingSection,
    RemoveSection,
    _ModSelect,
    Easy,
    NoFail,
    HalfTime,
    HardRock,
    SuddenDeath,
    DoubleTime,
    Hidden,
    Flashlight,
    Relax,
    Autopilot,
    SpunOut,
    Auto,
    ScoreV2
}

export enum VirtualKeyCode {
    A = 65,
    Add = 107,
    Apps = 93,
    Attn = 246,
    B = 66,
    Back = 8,
    BrowserBack = 166,
    BrowserFavorites = 171,
    BrowserForward = 167,
    BrowserHome = 172,
    BrowserRefresh = 168,
    BrowserSearch = 170,
    BrowserStop = 169,
    C = 67,
    CapsLock = 20,
    Crsel = 247,
    D = 68,
    D0 = 48,
    D1,
    D2,
    D3,
    D4,
    D5,
    D6,
    D7,
    D8,
    D9,
    '.' = 110,
    Delete = 46,
    '/' = 111,
    Down = 40,
    E = 69,
    End = 35,
    Enter = 13,
    EraseEof = 249,
    Escape = 27,
    Execute = 43,
    Exsel = 248,
    F = 70,
    F1 = 112,
    F10 = 121,
    F11,
    F12,
    F13,
    F14,
    F15,
    F16,
    F17,
    F18,
    F19,
    F2 = 113,
    F20 = 131,
    F21,
    F22,
    F23,
    F24,
    F3 = 114,
    F4,
    F5,
    F6,
    F7,
    F8,
    F9,
    G = 71,
    H,
    Help = 47,
    Home = 36,
    I = 73,
    Insert = 45,
    J = 74,
    K,
    L,
    LaunchApplication1 = 182,
    LaunchApplication2,
    LaunchMail = 180,
    LeftControl = 162,
    Left = 37,
    LeftAlt = 164,
    LeftShift = 160,
    LeftWindows = 91,
    M = 77,
    MediaNextTrack = 176,
    MediaPlayPause = 179,
    MediaPreviousTrack = 177,
    MediaStop,
    Multiply = 106,
    N = 78,
    None = 0,
    NumLock = 144,
    NumPad0 = 96,
    NumPad1,
    NumPad2,
    NumPad3,
    NumPad4,
    NumPad5,
    NumPad6,
    NumPad7,
    NumPad8,
    NumPad9,
    O = 79,
    ';' = 186,
    '\\' = 226,
    '?' = 191,
    '~',
    '[' = 219,
    '|',
    ']',
    '"',
    Oem8,
    OemClear = 254,
    ',' = 188,
    '-',
    '. ',
    '+' = 187,
    P = 80,
    Pa1 = 253,
    PageDown = 34,
    PageUp = 33,
    Play = 250,
    Print = 42,
    PrintScreen = 44,
    ProcessKey = 229,
    Q = 81,
    R,
    RightControl = 163,
    Right = 39,
    RightAlt = 165,
    RightShift = 161,
    RightWindows = 92,
    S = 83,
    Scroll = 145,
    Select = 41,
    SelectMedia = 181,
    Separator = 108,
    Sleep = 95,
    Space = 32,
    Subtract = 109,
    T = 84,
    Tab = 9,
    U = 85,
    Up = 38,
    V = 86,
    VolumeDown = 174,
    VolumeMute = 173,
    VolumeUp = 175,
    W = 87,
    X,
    Y,
    Z,
    Zoom = 251
}
