{"OsuVersion": "2025.816.0", "osu.Game.OsuGame": {"osuLogo": 1592, "ScreenStack": 1536, "<frameworkConfig>k__BackingField": 1464, "SentryLogger": 1496}, "osu.Framework.Game": {"<Host>k__BackingField": 824}, "osu.Game.OsuGameBase": {"<API>k__BackingField": 1080, "<SpectatorClient>k__BackingField": 1192, "<ScoreManager>k__BackingField": 1024, "<MultiplayerClient>k__BackingField": 1200, "beatmapClock": 1232, "<Storage>k__BackingField": 1088, "<Beatmap>k__BackingField": 1104, "<SkinManager>k__BackingField": 1040, "<VersionHash>k__BackingField": 976, "<LocalConfig>k__BackingField": 984, "rulesetConfigCache": 1176, "realm": 1216}, "osu.Game.Screens.SelectV2.SoloSongSelect": {"<game>k__BackingField": 1224}, "osu.Game.Screens.Play.SubmittingPlayer": {"<api>k__BackingField": 1248, "<spectatorClient>k__BackingField": 1256}, "osu.Game.Screens.Play.PlayerLoader": {"osuLogo": 1168}, "osu.Game.Beatmaps.FramedBeatmapClock": {"finalClockSource": 528, "decoupledTrack": 552}, "osu.Framework.Timing.DecouplingFramedClock": {"<Source>k__BackingField": 24}, "osu.Framework.Audio.Track.Track": {"length": 72}, "osu.Framework.Timing.FramedClock": {"<CurrentTime>k__BackingField": 48}, "osu.Game.Screens.OsuScreen": {"<logo>k__BackingField": 896}, "osu.Game.Screens.Ranking.SoloResultsScreen": {"<api>k__BackingField": 1040, "SelectedScore": 920}, "osu.Game.Screens.Edit.Editor": {"<realm>k__BackingField": 960, "<api>k__BackingField": 1096}, "osu.Game.Screens.OnlinePlay.OnlinePlayScreen": {"<API>k__BackingField": 960}, "osu.Game.Screens.OnlinePlay.Multiplayer.Multiplayer": {"<client>k__BackingField": 976}, "osu.Game.Screens.Spectate.SpectatorScreen": {"<spectatorClient>k__BackingField": 944, "gameplayStates": 992}, "osu.Game.Screens.OnlinePlay.Multiplayer.Spectate.MultiSpectatorScreen": {"<multiplayerClient>k__BackingField": 1032}, "osu.Game.Online.Multiplayer.OnlineMultiplayerClient": {"<IsConnected>k__BackingField": 728}, "osu.Game.Online.Multiplayer.MultiplayerClient": {"room": 648}, "osu.Game.Screens.Play.Player": {"<api>k__BackingField": 1008, "<scoreManager>k__BackingField": 1000, "<Score>k__BackingField": 1152, "<ScoreProcessor>k__BackingField": 1096, "<HealthProcessor>k__BackingField": 1104, "<HUDOverlay>k__BackingField": 1120, "dependencies": 1168}, "osu.Framework.Screens.ScreenStack": {"stack": 800}, "osu.Game.Rulesets.RulesetConfigCache": {"configCache": 520}, "osu.Game.Online.Chat.ExternalLinkOpener": {"<api>k__BackingField": 536}, "osu.Game.Online.API.APIAccess": {"<localUser>k__BackingField": 592, "game": 504}, "osu.Desktop.OsuGameDesktop": {"AvailableMods": 1128, "SelectedMods": 1120, "Ruleset": 1112}, "osu.Game.Scoring.ScoreInfo": {"<OnlineID>k__BackingField": 184, "<LegacyOnlineID>k__BackingField": 192, "<ModsJson>k__BackingField": 80, "<HitEvents>k__BackingField": 120, "<RealmUser>k__BackingField": 72, "<TotalScore>k__BackingField": 160, "<MaxCombo>k__BackingField": 204, "<Combo>k__BackingField": 212, "<Passed>k__BackingField": 220, "<Ruleset>k__BackingField": 48, "<Accuracy>k__BackingField": 176, "<Date>k__BackingField": 256, "statistics": 128, "maximumStatistics": 136, "user": 112}, "osu.Framework.Platform.Storage": {"<BasePath>k__BackingField": 8}, "osu.Game.IO.WrappedStorage": {"<UnderlyingStorage>k__BackingField": 16}, "osu.Game.Beatmaps.WorkingBeatmapCache+BeatmapManagerWorkingBeatmap": {"BeatmapInfo": 8, "BeatmapSetInfo": 16}, "osu.Game.Models.RealmUser": {"<Username>k__BackingField": 24, "<OnlineID>k__BackingField": 40}, "osu.Game.Rulesets.RulesetInfo": {"<OnlineID>k__BackingField": 48}, "osu.Game.Screens.Play.SoloPlayer": {"RestartCount": 916}, "osu.Game.Rulesets.Osu.Scoring.OsuScoreProcessor": {"Combo": 592}, "osu.Game.Rulesets.Scoring.ScoreProcessor": {"hitEvents": 648}, "osu.Game.Online.API.Requests.Responses.APIMe": {"PlayMode": 136}, "osu.Game.Online.API.Requests.Responses.APIUser": {"<Id>k__BackingField": 232, "<Username>k__BackingField": 8, "countryCodeString": 32, "statistics": 160}, "osu.Game.Users.UserStatistics": {"RankedScore": 32, "GlobalRank": 84, "PlayCount": 56, "Accuracy": 40, "Level": 76, "PP": 104}, "osu.Game.Rulesets.Osu.Scoring.OsuHealthProcessor": {"Health": 560}, "osu.Game.Screens.Play.HUDOverlay": {"InputCountController": 840}, "osu.Game.Screens.Play.HUD.InputCountController": {"triggers": 512}, "osu.Game.Rulesets.UI.DrawableRuleset": {"<ReplayScore>k__BackingField": 808}, "osu.Game.Beatmaps.BeatmapInfo": {"<OnlineID>k__BackingField": 140, "<MD5Hash>k__BackingField": 88, "<StatusInt>k__BackingField": 136, "<Metadata>k__BackingField": 48, "<Difficulty>k__BackingField": 40, "<DifficultyName>k__BackingField": 24, "<TotalObjectCount>k__BackingField": 148, "<Hash>k__BackingField": 80}, "osu.Game.Beatmaps.BeatmapSetInfo": {"<OnlineID>k__BackingField": 48}, "osu.Game.Beatmaps.BeatmapMetadata": {"<Title>k__BackingField": 24, "<TitleUnicode>k__BackingField": 32, "<Artist>k__BackingField": 40, "<ArtistUnicode>k__BackingField": 48, "<Author>k__BackingField": 56, "<Source>k__BackingField": 64, "<Tags>k__BackingField": 72, "<UserTags>k__BackingField": 80, "<PreviewTime>k__BackingField": 104, "<AudioFile>k__BackingField": 88, "<BackgroundFile>k__BackingField": 96}, "osu.Game.Beatmaps.BeatmapDifficulty": {"<DrainRate>k__BackingField": 40, "<CircleSize>k__BackingField": 44, "<OverallDifficulty>k__BackingField": 48, "<ApproachRate>k__BackingField": 52, "<SliderMultiplier>k__BackingField": 24, "<SliderTickRate>k__BackingField": 32}, "osu.Game.Online.Multiplayer.MultiplayerRoomUser": {"UserID": 40, "<State>k__BackingField": 44, "<BeatmapAvailability>k__BackingField": 8, "<Mods>k__BackingField": 16, "<MatchState>k__BackingField": 24, "RulesetId": 48, "BeatmapId": 56, "<User>k__BackingField": 32}, "osu.Game.Skinning.SkinManager": {"CurrentSkin": 80}, "osu.Game.Skinning.Skin": {"<Name>k__BackingField": 64}, "osu.Framework.Platform.GameHost": {"<inputConfig>k__BackingField": 48}, "osu.Framework.Configuration.InputConfigManager": {"<InputHandlers>k__BackingField": 32}, "osu.Framework.Input.Handlers.Tablet.OpenTabletDriverHandler": {"<AreaOffset>k__BackingField": 72, "<AreaSize>k__BackingField": 80, "<Rotation>k__BackingField": 88, "<PressureThreshold>k__BackingField": 96}, "osu.Framework.Input.Handlers.Mouse.MouseHandler": {"<UseRelativeMode>k__BackingField": 40}, "osu.Game.Utils.SentryLogger": {"sentrySession": 16}, "Sentry.SentrySdk+DisposeHandle": {"_localHub": 8}, "Sentry.Internal.Hub": {"_options": 32}, "Sentry.SentryOptions": {"<Release>k__BackingField": 176}}