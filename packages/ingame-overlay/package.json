{"name": "@tosu/ingame-overlay", "version": "4.13.0", "description": "High performance tosu ingame overlay", "private": true, "main": "./dist/src/index.js", "scripts": {"build": "electron-vite build", "dev": "electron-vite dev", "dist": "npm run build && electron-builder --windows"}, "keywords": [], "author": "storycraft <<EMAIL>>", "dependencies": {"asdf-overlay-node": "^0.7.5"}, "devDependencies": {"@eslint/js": "^9.26.0", "@types/node": "^22.15.14", "electron": "^37.2.5", "electron-builder": "^26.0.12", "electron-vite": "^4.0.0", "typescript": "^5.8.3"}, "build": {"productName": "tosu-ingame-overlay", "asar": true, "files": ["dist"], "electronLanguages": "en-US", "win": {"target": [{"target": "dir", "arch": ["x64"]}], "icon": "./assets/tosu.ico"}, "directories": {"output": "./pack/", "app": "."}, "publish": {"provider": "github"}}, "os": ["win32"]}