:root {
  --text-1: #647a91;
  --text-2: #8ca0b5;
  --text-links: #6593c5;
  --background-1: #191e25;

  --block-1: #242d37;
  --block-2: #333f4c;
  --block-on-block: 211.25deg, 75.28%, 68.43%;
  --block-color-1: #97a7b9;

  --button-update: #76c757;
  --button-download: #568cc7;
  --button-delete: #d57397;
  --button-open: #73a6d5;
  --button-open-folder: #b7a375;
  --button-save: #73a6d5;
  --button-cancel: #d57381;
  --button-builder: #d5b873;
  --button-add-option: #9cd573;
  --button-update-settings: #73a6d5;

  --submit-delete: #56c763;

  --notification-block-green: #56c763;
  --notification-color-green: #191e25;
  --notification-block-yellow: #d5ce73;
  --notification-color-yellow: #252419;
  --notification-block-red: #d57397;
  --notification-color-red: #191e25;

  --search-color-2: #5c6e80;
  --search-color-1: #a6b8ca;

  --search-block: #283139;
  --search-block-border: #4c5660;

  --input-color-2: #707f8d;
  --input-color-1: #cbd8e5;
  --input-block: #3f4e5d;

  --header-color: #939393;
  --header-background: #14171c;
}

* {
  position: relative;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  min-width: 0;
}

::selection {
  color: var(--background-1);
  background-color: var(--text-links);
}

::-webkit-scrollbar {
  width: 16px;
}

::-webkit-scrollbar-thumb {
  background-color: var(--text-links);
  border-radius: 0.2em;
}

button {
  font-size: 1em;
  font-family: inherit;
  font-weight: inherit;
  font-style: inherit;
  border: 0;
  outline: 0;
}

img {
  display: flex;
}

body {
  font-size: 20px;
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  font-style: normal;
  color: var(--text-1);
}

body:not(.ingame) {
  justify-content: center;
  flex-direction: column;
  display: flex;
  max-width: min(1280px, 100vw - 2em);
  min-height: 100vh !important;
  margin: 0 auto;
  background-color: var(--background-1);
  overflow-x: hidden;
}

iframe {
  pointer-events: none;
  user-select: none;
}

select {
  font-size: 1em;
  color: var(--input-color-1);
  padding: 0.3em 0.4em;
  background: var(--input-block);
  border-radius: 0.2em;
  outline: 0;
  border: 0;
}

header {
  position: sticky;
  font-size: 0.9em;
  color: var(--header-color);
  top: 0;
  gap: 1.2em;
  margin: 0 1em;
  // padding: 0.4em 0;
  height: 2em;
  z-index: 3;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -1000px;
    right: -1000px;
    bottom: 0;
    background: var(--header-background);
  }

  a:not(.button) {
    user-select: none;
    text-decoration: none;
    color: var(--header-color) !important;
    gap: 0.7em;
    transition: filter 0.2s ease;

    img {
      width: 1.2em;
      height: 1.2em;
    }

    &:hover {
      filter: brightness(1.2);
    }
  }
}

.imglink {
  font-size: 0.9em;
}

body:not(.ingame) main {
  display: grid;
  min-height: 0;
  margin-top: 2em;
  margin-bottom: 3em;
}

footer {
  font-weight: 600;
  text-align: center;
  font-size: 0.8em;
  position: sticky;
  bottom: 0;
  padding: 0.6em 0;
  margin-top: auto;

  &::before {
    content: "";
    pointer-events: none;
    mask-image: linear-gradient(0deg, black 0%, transparent 100%);
    position: absolute;
    height: 7em;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-1);
    z-index: 0;
  }

  &>* {
    z-index: 1;
  }
}

a {
  color: var(--text-links);
}

.flexer {
  justify-content: flex-start;
  align-items: center;
  display: flex;
  flex-wrap: wrap;
}

.links {
  gap: 1em;
  padding: 0 1em;
  margin-bottom: 2em;
  z-index: 2;

  .button {
    gap: 0.5em;

    &.active {
      color: var(--button-open);
      background: var(--background-1);
    }
  }
}

.tabs {
  justify-content: center;
  position: sticky;
  top: 2em;

  width: calc(100% - 1em);
  margin: 0 auto;

  gap: 1em;
  z-index: 1;

  .tab-item {
    justify-content: center;
    align-items: center;
    display: flex;

    gap: 0.5em;
    line-height: 1em;

    cursor: pointer;
    color: var(--text-1);
    padding: 0.6em;

    text-decoration: none;

    transition: 0.1s ease;
    transition-property: color, text-decoration-color;

    &.active {
      color: var(--text-links);
      text-decoration-color: var(--text-links);

      &::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 2px;
        background: var(--text-links);
        border-radius: 10em;
      }
    }


  }

  &::before {
    content: '';
    position: absolute;
    top: -2em;
    left: -3em;
    right: -3em;
    bottom: 0;
    background: var(--background-1);
  }
}

.indent-left {
  margin-left: auto;
}

.indent-right {
  margin-right: auto;
}

.results {
  display: grid;

  gap: 0.5em;
}

.no-results {
  text-align: center;
  width: 100%;
  margin: 2em 0;
}

.result-item {
  display: grid;
  gap: 0.8em;
  color: var(--block-color-1);
  padding: 1em;
  background: var(--block-1);
  border-radius: 0.3em;


  hr {
    border-color: var(--background-1);
  }

  .ri-gallery {
    gap: 0.4em;

    img {
      max-width: calc(50% - 0.2em);
      max-height: 10em;
      border-radius: 0.4em;
    }
  }

  .ri-links {
    font-size: 0.9em;
    gap: 0.4em;
    margin-top: 0.4em;

    img {
      height: 1.2em;
      border-radius: 0.2em;
    }
  }

  .ri-footer {
    font-size: 0.9em;
    gap: 1em;
  }

  &.downloaded::before,
  &.updatable::before {
    position: absolute;
    pointer-events: none;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 1;
    z-index: 1;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    font-size: 1.4em;
    padding: 0.5em;
    color: #ffffff;
    text-shadow: 0 0 0.2em black, 0 0.1em 0.1em black, 0 0.1em 0.7em black, 0 0.1em 0.2em black;
  }

  &.downloaded {
    outline: 2px solid;

    &::before {
      content: "already downloaded";
      background: hsl(211.76deg 19.54% 65.88% / 20%);
    }
  }

  &.updatable {
    outline: 2px solid #a7d395;

    &::before {
      content: "update available";
      color: #a7d395;
      background: hsl(103deg 19.54% 65.88% / 20%);
    }
  }
}

.copyable {
  user-select: none;
  cursor: pointer;
  padding: 0.1em 0.2em;
  background: hsl(var(--block-on-block), 0.2);
  transition: 0.5s ease;
  transition-property: background, filter;
  border-radius: 0.2em;


  &:active {
    filter: brightness(2);
    background: hsl(var(--block-on-block), 0.4);
    transition: 0.1s ease;
  }
}


.notification {
  font-size: 0.8em;
  font-weight: bold;
  position: absolute;
  left: 50%;
  bottom: 2em;
  padding: 0.5em 1.6em;
  translate: 0 0;
  box-shadow: 0 0.7em 1.2em hsl(0deg 0% 0% / 70%);
  transition: 0.3s ease;
  transition-property: translate, opacity;
  border-radius: 0.3em;
  z-index: 15;

  &.hidden {
    opacity: 0;
    translate: 0 1em;
  }



  &.red {
    color: var(--notification-color-red);
    background: var(--notification-block-red);
  }



  &.yellow {
    color: var(--notification-color-yellow);
    background: var(--notification-block-yellow);
  }

  &.green {
    color: var(--notification-color-green);
    background: var(--notification-block-green);
  }
}

@keyframes spin {
  0% {
    rotate: 0;
  }

  100% {
    rotate: 360deg;
  }
}

.button {
  white-space: nowrap;
  text-decoration: none;
  font-size: 0.9em;
  font-weight: 700;
  cursor: pointer;
  padding: 0.4em 1em;
  border-radius: 0.4em;

  transition: 0.1s ease;
  transition-property: translate, filter;

  &>* {
    pointer-events: none;
  }

  span {
    display: block;
    pointer-events: none;

    transition: 0.3s ease;
    transition-property: opacity, width;
  }

  img {
    width: 1em;
    height: 1em;

    object-fit: cover;

    transition: 0.3s ease;
    transition-property: opacity, width, height;

    animation: spin 1s infinite forwards linear;
  }

  &:hover {
    filter: brightness(1.1);
    translate: 0 -0.05em;
  }

  &:active {
    filter: brightness(0.9);
    translate: 0 0.1em;
  }

  &.disable {
    user-select: none;
    pointer-events: none;

    filter: grayscale(1);
  }

  &.small {
    padding: 0.1em 0.5em;
  }
}

.buttons-group {
  font-size: 0.9em;
  gap: 10px;
  height: 1.8em;

  &>* {
    height: 100%;
  }
}

.dl-button {
  color: var(--background-1);
  background: var(--button-download);
}

.update-button {
  color: var(--background-1);
  background: var(--button-update);
  box-shadow: 0em 0.4em 3em hsl(from var(--button-update) h s l / 50%);
}

.delete-button {
  color: var(--background-1);
  background: var(--button-delete);
}

.open-button {
  color: var(--background-1);
  background: var(--button-open);
}

.open-folder-button {
  color: var(--background-1);
  background: var(--button-open-folder);
}

.settings-builder-button {
  display: none;
  color: var(--background-1);
  background: var(--button-builder);

  &.active {
    display: unset;
  }
}



.add-option-button {
  color: var(--background-1);
  background: var(--button-add-option);
}

.save-button {
  color: var(--background-1);
  background: var(--button-save);
}

.submit-button {
  display: block;
  color: var(--background-1);
  padding: 0.2em 1em;
  background: var(--submit-delete);
}

.remove-option-button,
.cancel-button {
  color: var(--background-1);
  background: var(--button-cancel);
}

.open-link-button,
.update-settings-button,
.update-x2-settings-button {
  color: var(--background-1);
  background: var(--button-update-settings);
}

.open-link-button {
  gap: 0.5em;
}

.search-bar {
  font-family: inherit;
  font-weight: 600;
  font-size: 1em;
  position: sticky;
  top: 4.2em;
  color: var(--search-color-1);
  padding: 0.4em 0.8em;
  margin-bottom: 0.5em;
  background: var(--search-block);
  border-radius: 0.3em;
  outline: 0;
  border: 2px solid var(--search-block-border);
  transition: 0.2s ease;
  transition-property: filter, opacity, background, border;
  z-index: 2;

  &::placeholder {
    color: var(--search-color-2);
  }

  &.disable {
    user-select: none;
    pointer-events: none;

    filter: grayscale(1);
    opacity: 0.5;
  }

  &:hover {
    background: hsl(from var(--search-block) h calc(s - 0) calc(l + 10));
  }

  &:focus {
    background: hsl(from var(--search-block) h calc(s - 0) calc(l - 10));
    border: 2px solid hsl(from var(--search-block-border) h calc(s - 0) calc(l + 20));
  }
}

.input {
  font-size: 1em;
  font-family: inherit;
  color: var(--input-color-1);
  background: var(--input-block);
  padding: 0.2em 0.6em;
  border-radius: 0.2em;
  outline: 0;
  border: 0;
  transition: 0.2s ease;
  transition-property: filter, opacity;

  &::placeholder {
    color: var(--input-color-2);
  }

  &.disable {
    user-select: none;
    pointer-events: none;

    filter: grayscale(1);
    opacity: 0.5;
  }
}

@keyframes shake {

  10%,
  26% {
    transform: translateX(0);
  }

  14% {
    transform: translateX(-5px);
  }

  18% {
    transform: translateX(5px);
  }

  22% {
    transform: translateX(-5px);
  }
}

.settings {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  padding: 0.8em;
  color: var(--block-color-1);
  background: var(--block-1);
  border-radius: 0.3rem;

  .settings-item-group {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;

    &>p {
      font-weight: bold;
      color: var(--block-color-1);
      margin-left: 0.4rem;
      font-size: 1.3rem;
    }

    &>div {
      display: flex;
      flex-direction: column;
      background-color: #191e25;
      border-radius: 0.3rem;
      padding: 1rem;
      gap: 1rem;
    }
  }

  .settings-item-v2 {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;

    /* Div containing input1, input2 and setting name */
    &>div {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 0.4rem;

      /* Setting name */
      &>p:nth-child(2) {
        font-size: 1.2rem;
        font-weight: bold;
        opacity: 50%;
        transition: opacity 0.2s ease, color 0.2s ease;
      }
    }

    /* Setting description */
    &>p:nth-child(2) {
      font-size: 1rem;
      opacity: 50%;
      transition: opacity 0.2s ease, color 0.2s ease;
    }

    &:has(.textarea-input) {
      &>div>p:nth-child(1) {
        font-weight: bold;
        color: #6593c5;
      }

      &>p:nth-child(3) {
        font-size: 1rem;
        opacity: 60%;
      }
    }

    &:has(.text-input):has(.switch-thumb:not(:checked)) {
      &>div {
        cursor: not-allowed;
      }

      &>div>.text-input {
        opacity: 60%;
        pointer-events: none;
      }
    }

    &:has(.switch-thumb:checked) {
      &>div>p:nth-child(2) {
        opacity: 1;
        color: #6593c5;
      }

      &>p:nth-child(2) {
        opacity: 70%;
      }

      &>.text-input {
        opacity: 1;
        pointer-events: all;
      }
    }

    &:has(div > .number-input) {
      & div > p {
        color: #6593c5;
        font-weight: bold;
        opacity: 100%;
      }

      &:has(div > .number-input[data-id='POLL_RATE']) div > p,
      &:has(div > .number-input[data-id='PRECISE_POLL_RATE']) div > p {
        width: 16ch;
    }

      & p {
        opacity: 70%;
      }
    }
  }

  .switch {
    position: relative;
    display: inline-block;
    width: 3rem;
    height: 1.7rem;

    input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    span {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #242d37;
      transition: 0.2s;
      border-radius: 1.7rem;

      &::before {
        position: absolute;
        content: "";
        height: 1.3rem;
        width: 1.3rem;
        left: 0.2rem;
        bottom: 0.2rem;
        background-color: #7a91a9;
        transition: 0.2s;
        border-radius: 50%;
      }
    }

    input:checked+span {
      background-color: #496079;

      &::before {
        background-color: #88afd9;
        transform: translateX(1.3rem);
      }
    }
  }

  .number-input {
    display: flex;
    align-items: center;
    height: fit-content;
    background-color: #242d37;
    border-radius: 0.3em;

    input[type='number'] {
      width: 3rem;
      height: 1.7rem;
      color: var(--block-color-1);
      background-color: transparent;
      border: 0;
      outline: 0;
      font-family: monospace;
      font-size: 1rem;
      line-height: 1.7rem;
      text-align: center;
      font-weight: bold;

      &::-webkit-inner-spin-button,
      &::-webkit-outer-spin-button {
        appearance: none;
        -webkit-appearance: none;
        margin: 0;
      }

      -moz-appearance: textfield;
    }

    button {
      width: 1.7rem;
      height: 1.7rem;
      font-size: 1em;
      color: var(--block-color-1);
      background-color: transparent;
      cursor: pointer;

      &:focus {
        color: initial;
      }
    }

    &:hover,
    &:focus-within {

      button,
      input {
        color: #6593c5;
      }
    }
  }

  .text-input {
    display: flex;
    align-items: center;
    background-color: #242d37;
    border-radius: 0.3em;
    width: fit-content;
    height: fit-content;
    transition: opacity 0.2s ease, color 0.2s ease;

    input[type='text'] {
      display: flex;
      field-sizing: content;
      padding: 0 0.6em;
      border: 0;
      outline: 0;
      font-size: 1rem;
      height: 1.7rem;
      min-width: 2rem;
      width: 100%;
      line-height: 1.7rem;
      font-family: monospace;
      text-align: center;
      font-weight: bold;
      background: transparent;
      color: var(--block-color-1);
    }

    &:hover,
    &:focus-within {
      input {
        color: #6593c5;
      }
    }


    &.-keybind {
      width: 17em;
    }
  }

  .textarea-input {
    display: flex;
    align-items: center;
    background-color: #242d37;
    border-radius: 0.3em;
    width: fit-content;
    height: fit-content;

    textarea {
      display: flex;
      padding: 0 0.6em;
      border: 0;
      outline: 0;
      font-size: 1rem;
      width: fit-content;
      min-width: 20rem;
      max-width: 40rem;
      height: fit-content;
      min-height: 4rem;
      max-height: 10rem;
      field-sizing: content;
      line-height: 1.7rem;
      font-family: monospace;
      text-align: left;
      font-weight: bold;
      background: transparent;
      color: var(--block-color-1);
    }

    &:hover,
    &:focus-within {
      textarea {
        color: #6593c5;
      }
    }
  }

  .settings-item-v2:has(div > .text-input) {
    &:not(:has(div > .switch)) {
      & div>p {
        color: #6593c5;
        font-weight: bold;
        opacity: 100%;
        margin-right: 0.4rem;
      }

      & p {
        opacity: 60%;
      }
    }
  }

  .settings-save-button {
    position: sticky;
    bottom: 2rem;
    opacity: 25%;
    z-index: 10;
    transition: ease-in-out;
    transition-duration: 0.5s;
    transition-property: opacity;

    &>button {
      background-color: #6593c5;

      &:disabled {
        pointer-events: none;
      }
    }

    &.shake {
      animation: shake 2s ease infinite;
    }
  }
}

.si {

  &:not(.-s):not(.block) div:nth-child(1) {
    width: 20em;
  }

  h4,
  .settings-note {
    color: #7a91a9;
  }

  p,
  .settings-note {
    color: #7a91a9;
  }

  p,
  .settings-note {
    filter: brightness(0.8);
    font-size: 0.8em;
    max-width: 20em;
  }

  .settings-note {
    margin-top: 0.5em;
    font-style: italic;
  }

  input[type="text"],
  input[type="number"],
  input[type="password"],
  textarea {
    font-size: 0.9em;
    font-family: inherit;
    color: var(--input-color-1);
    width: 20em;
    padding: 0.2em 0.6em;
    background: var(--input-block);
    outline: 0;
    border-radius: 0.2em;
    border: 0;
    transition: 0.2s ease;
    transition-property: filter, opacity, outline, background;
    outline: 2px solid transparent;

    &::placeholder {
      color: var(--input-color-2);
    }

    &:hover {
      background: #4e5e6f;
    }

    &:focus {
      background: #2b353f;
      outline: 2px solid var(--input-block);
    }
  }

  input[type="color"] {
    padding: 0;
    background: transparent;
    border: 0;
    outline: 0;
    width: 7em;
    block-size: 2.3em;
    flex-grow: unset;
    border-radius: 20em;
  }

  &.block {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5em;
  }
}

.si-checkbox {
  justify-content: center;
  align-items: center;
  cursor: pointer;
  display: flex;
  gap: 0.4em;
  height: 1em;

  input {
    display: none;
  }

  input:checked~.checkmark::before {
    scale: 0.7;
    background: var(--input-color-1);
  }

  input:checked~.status::before {
    content: 'Enabled';
  }


  .checkmark {
    background: #637a91;
    width: 1em;
    height: 1em;
    border-radius: 0.2em;
    overflow: hidden;
    display: block;

    &::before {
      content: '';
      display: block;
      width: 1em;
      height: 1em;
      scale: 1;
      background: #637a91;
      transition: background 0.1s ease, scale 0.3s ease;
      border-radius: 0.2em;
    }
  }

  .status {
    &::before {
      content: 'Disabled';
    }
  }

  &.sic-start {
    justify-content: flex-start;
  }
}

.si-header {
  color: var(--text-2);
  border-bottom: 1px solid;
  padding-bottom: 0.1em;
}

.si-btn {
  gap: 1em;
}


.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: background 0.5s ease, backdrop-filter 0.6s ease, transform 0.15s ease;
  z-index: 10;

  &:not(.hidden) {
    background: hsl(0deg 0% 0% / 50%);
    backdrop-filter: opacity(0.5) blur(50px);

    .m-content {
      opacity: 1;
      transform: translate(-50%, -50%) translateY(0em);
    }
  }

  &#settingsBuilder {
    .m-content {
      width: min(1280px, 100vw - 2em);
    }

    .m-scroll {
      gap: 1em;
    }
  }

  .m-content {
    width: min(1100px, 100vw - 2em);
    top: 50%;
    left: 50%;
    padding: 1em;
    opacity: 0;
    background: var(--block-1);
    border-radius: 0.4em;
    box-shadow: 0 1em 2em hsl(0deg 0% 0% / 50%), 0 1em 4em hsl(0deg 0% 0% / 50%);
    transform: translate(-50%, -50%) translateY(2em);
    transition: opacity 0.3s ease, transform 0.3s ease;
    z-index: 1;
  }

  .ms-title {
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    display: flex;
    padding-bottom: .1em;
    margin-bottom: .8em;
    border-bottom: 1px solid;

    span:nth-child(2) {
      font-size: 0.7em;
    }
  }

  .ms-btns {
    font-size: 0.9em;
    margin-top: 1.5em;
  }

  .m-scroll {
    display: grid;
    gap: .8em;
    max-height: min(35em, 100vh - 11em);
    overflow: auto;

    &::-webkit-scrollbar {
      width: 10px;
    }
  }

  &.-expand {
    transform: scale(1.02);
  }
}


.update-available {
  font-weight: 700;
  font-size: 0.9em;
  user-select: none;
  cursor: pointer;
  color: var(--background-1);
  height: 3em;
  padding: 0.4em 0;
  transition: padding 0.4s ease, height 0.4s ease;


  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -30vw;
    right: -30vw;
    bottom: 0;
    background: #56c763;
  }

  ::selection {
    color: #56c763;
    background-color: var(--background-1);
  }

  div:nth-child(2) {
    font-size: 0.8em;
  }

  &.hidden {
    display: none;
  }

  &>* {
    pointer-events: none;
  }

  &.loadong {
    animation: update 2s infinite forwards ease;
  }

  &.fold {
    padding: 0 0;
    height: 0;
  }
}


@keyframes update {
  0% {
    opacity: 1;
  }

  70% {
    opacity: 0.6;
  }

  100% {
    opacity: 1;
  }
}

.settings-container {
  padding-left: 0.5em;
  padding-right: 0.5em;
}

.new-item,
.settings-item {
  display: grid;
  gap: 0.5em;
  border-radius: 0.6em;

  // select {
  //   margin-right: 1em;
  // }

  input,
  textarea {
    font-size: 1em !important;
    padding: 0.3em .6em !important;
  }

  .ni-unique input {
    width: 12em;
  }

  .ni-row-1 {
    justify-content: flex-start;
    align-items: center;
    display: flex;
    gap: 0.5em;
  }

  .ni-options {
    display: grid;
    gap: 0.5em;
  }

  .nid-value {
    white-space: nowrap;
    flex-wrap: nowrap !important;
    gap: 0.5em;

    select {
      flex-grow: 1;
    }
  }

  .ni-p {
    margin-bottom: 0.5em;
  }

  .ni-commands {
    display: grid;
    gap: 0.5em;
    margin-bottom: 1em;
  }

  .nic-container {
    display: grid;
    gap: 0.5em;
  }

  .nic-content {
    gap: 0.5em;
    flex-wrap: nowrap !important;

    input[placeholder="name"] {
      width: 10em;
    }

    input[placeholder="description"] {
      flex-grow: 1;
      width: unset;
    }

    input[placeholder="default value"] {
      width: 10em;
    }
  }

  .nic-options,
  .ni-options {
    grid-template-columns: repeat(4, minmax(0, 1fr));
    grid-auto-rows: minmax(min-content, max-content);
    display: grid;
    gap: 0.5em;
    margin-top: 0.5em;
    margin-bottom: 0.5em;

    .ni-option input {
      width: unset;
      field-sizing: content;
    }
  }

  .ni-option input {
    flex-grow: 1;
    outline: 2px solid transparent;
    transition: outline 0.1s ease, background 0.1s;
  }

  .ni-option i,
  .nic-button {
    cursor: pointer;
    padding: 0.4em .6em;

    &:hover {
      filter: brightness(1.4);
    }

    &:active {
      filter: brightness(1);
    }
  }

  .ni-value input {
    width: 100%;
  }

  .ni-value input[type="color"] {
    padding: 0em 0.1em !important;
    background: var(--input-block);
    border: none;
    outline: none;
  }

  .ni-title {
    flex-grow: 2;

    input {
      width: 100%;
    }
  }

  .ni-description {
    width: 100%;

    textarea {
      // field-sizing: content;
      width: 100%;
    }
  }

  .si-checkbox {
    width: fit-content;
  }

  .highlight {
    background: hsla(352, 47%, 68%, 0.4) !important;
    // outline: 2px solid #d57381 !important;
  }
}

.settings-item {
  padding: 0.4em;
}

.new-item {
  color: var(--text-1);
  padding: 1em;
  background: var(--background-1);
}

.si-btns {
  font-size: 0.9em;
}

hr.modal-space {
  margin: 1em 0;
  border: 1px solid;
}


.sbi {
  display: grid;
  gap: 0.5em;


}

.m-scroll>.sbi:not(:nth-child(2)) {
  padding-top: 1em;
  border-top: 2px solid;
  margin-top: 0.5em;
}


.m-scroll>*:not(:first-child) .si-header {
  margin-top: 1em;
}

.oab {
  font-size: 0.8em;
}


.devmode {
  justify-content: flex-end;
  font-size: 0.9em;
  width: 10.5em;

  &>* {
    pointer-events: none;
    user-select: none;
  }
}

.external-exists {
  user-select: none;
  font-size: 0.7em;
  color: var(--background-1);
  gap: 0.5em;
  padding: .2em 0.6em;
  margin-left: 1.4em;
  background: var(--button-builder);
  border-radius: .2em;
}

.txt-area {
  align-items: flex-start;
  flex-direction: column;

  textarea {
    field-sizing: content;
    font-size: 1em;
    width: 100%;
  }
}

.fgrow {
  flex-grow: 1;
}


.sc-commands {
  counter-reset: command-counter;
  display: grid;
  gap: 0.5em;
  width: 100%;
}

.scc-item {
  align-items: flex-end;
  counter-increment: command-counter;
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  grid-auto-rows: minmax(min-content, max-content);
  gap: 0.5em 1em;
  width: 100%;
  color: var(--text-1);
  padding: 1em;
  background: var(--background-1);
  border-radius: 0.4em;

  input,
  label,
  select {
    width: 100% !important;
    // field-sizing: content;
    height: 1.5em;
    // flex-grow: 1;
  }

  select {
    padding: 0 0.4em;
  }

  p {
    margin-bottom: 0.5em;
  }


  // &:before {
  //   content: counter(command-counter);
  // }

  // .scci-item {
  //   display: grid;
  //   gap: 0.5em;
  // }

  &.empty {
    color: inherit;
    padding: unset;
    background: transparent;
  }
}

.ni-dcommands {
  display: grid;
  margin: 0.5em 0;
}

.mtb-1 {
  margin: 1em 0;
}

.g05 {
  gap: 0.5em;
}

.ssb {
  position: sticky;
  bottom: 1.5em;
  z-index: 1;
}


.results.--settings {
  .m-content {
    padding: 1em;
    background: var(--block-1);
    border-radius: 0.4em;
    box-shadow: 0 1em 2em hsl(0deg 0% 0% / 50%), 0 1em 4em hsl(0deg 0% 0% / 50%);
  }

  .ms-title {
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    position: sticky;
    top: 1.2em;
    display: flex;
    padding-bottom: .1em;
    margin-bottom: .8em;
    background: var(--block-1);
    border-bottom: 1px solid;
    z-index: 1;

    span:nth-child(2) {
      font-size: 0.7em;
    }
  }

  .ms-btns {
    font-size: 0.9em;
    position: sticky;
    margin-top: 1.5em;
    bottom: 2rem;
    z-index: 2;
  }

  .m-scroll {
    display: grid;
    gap: .8em;
    z-index: 0;
  }
}


.ingame_keybind {
  width: 25em !important;
}