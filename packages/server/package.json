{"name": "@tosu/server", "version": "0.0.1", "description": "", "author": "cyperdark", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepare": "npm run build && sass --style compressed --no-source-map ./assets/homepage.scss ./assets/homepage.min.css && postcss ./assets/homepage.min.css -o ./assets/homepage.min.css --use autoprefixer --no-map", "build": "tsc"}, "dependencies": {"@kotrikd/rosu-pp": "3.1.0", "@tosu/common": "workspace:*", "@tosu/updater": "workspace:*", "semver": "^7.7.1", "systeminformation": "^5.27.6", "ws": "^8.18.0"}, "devDependencies": {"@types/semver": "^7.7.0", "@types/ws": "^8.18.0", "autoprefixer": "^10.4.21", "postcss-cli": "^11.0.1", "sass": "^1.89.1"}}