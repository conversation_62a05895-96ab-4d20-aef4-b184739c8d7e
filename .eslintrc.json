{"root": true, "extends": ["standard", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"semi": ["error", "always"], "no-var": "error", "no-unused-vars": "off", "guard-for-in": "error", "@typescript-eslint/no-unused-vars": ["error", {"vars": "all", "args": "after-used", "ignoreRestSiblings": true, "argsIgnorePattern": "^_"}], "prefer-const": ["error", {"destructuring": "all"}], "standard/no-callback-literal": "off"}, "parserOptions": {"ecmaVersion": 6, "sourceType": "module"}, "overrides": [{"files": "*.ts", "rules": {"no-undef": "off", "no-redeclare": "off", "@typescript-eslint/no-redeclare": ["error"], "no-use-before-define": "off", "guard-for-in": "off"}}, {"files": "*.d.ts", "rules": {"no-useless-constructor": "off", "@typescript-eslint/no-unused-vars": "off"}}]}