{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "name": "run tosu",
            "request": "launch",
            "args": ["src/index.ts"],
            "outputCapture": "std",
            "runtimeArgs": ["--nolazy", "--inspect", "--expose-gc", "--inspect", "-r", "ts-node/register", "-r", "tsconfig-paths/register"],
            "cwd": "${workspaceRoot}/packages/tosu",
        },
        {
            "type": "node",
            "name": "Current TS File",
            "request": "launch",
            "args": ["${relativeFile}"],
            "outputCapture": "std",
            "runtimeArgs": ["--nolazy", "--inspect", "--expose-gc", "-r", "ts-node/register", "-r", "tsconfig-paths/register"],
            "cwd": "${workspaceRoot}/packages/tosu",
        }
    ]
}