{"author": "<PERSON>", "license": "GPL-3.0", "version": "4.13.0", "packageManager": "pnpm@10.10.0", "scripts": {"prepare": "husky install", "start": "pnpm run -C packages/tosu run:dev", "build:win": "pnpm run -C packages/tosu compile:win", "build:linux": "pnpm run -C packages/tosu compile:linux", "build:overlay": "pnpm run -C \"packages/ingame-overlay\" dist", "release": "standard-version", "prettier:fix": "prettier --write \"**/*.{js,jsx,ts,tsx,css}\"", "prettier:ci": "prettier --check \"**/*.{js,jsx,ts,tsx,css}\"", "lint:ci": "eslint --ext .ts,.d.ts .", "lint:fix": "eslint --fix --ext .ts,.d.ts ."}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^20.11.1", "@typescript-eslint/eslint-plugin": "^8.27.0", "@typescript-eslint/parser": "^8.27.0", "eslint": "^8", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "husky": "^9.1.7", "lint-staged": "^15.5.0", "prettier": "^3.5.3", "standard": "^17.1.0", "standard-version": "^9.5.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2"}, "lint-staged": {"**/*.{js,ts}": ["pnpm run prettier:fix", "pnpm run lint:fix"]}, "homepage": "https://github.com/tosuapp/tosu#readme", "repository": {"type": "git", "url": "git+https://github.com/tosuapp/tosu.git"}, "bugs": {"url": "https://github.com/tosuapp/tosu/issues"}, "engines": {"node": ">=20.11.1"}}